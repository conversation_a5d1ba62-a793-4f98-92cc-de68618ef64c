from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from typing import List
import uvicorn
import logging
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="中文文档混合检索系统", version="2.0.0")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 导入模型和服务
from .models import (
    SearchRequest, SearchResponse, SearchResult,
    StatsResponse, UploadResponse, HealthResponse
)
from .services.hybrid_search_service import HybridSearchService

# 初始化服务
search_service = HybridSearchService()

# 启动时自动加载文档
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    logger.info("正在启动应用程序...")

    # 自动加载data/documents目录中的文档
    logger.info("正在加载预置文档...")
    success = search_service.load_documents_from_directory("data/documents")
    if success:
        logger.info("预置文档加载成功")
    else:
        logger.warning("预置文档加载失败或目录为空")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页"""
    try:
        with open("static/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <head><title>中文文档混合检索系统</title></head>
            <body>
                <h1>中文文档混合检索系统</h1>
                <p>前端页面正在构建中...</p>
                <p>请访问 <a href="/docs">/docs</a> 查看API文档</p>
            </body>
        </html>
        """)

@app.post("/upload", response_model=UploadResponse)
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    try:
        file_contents = []

        for file in files:
            if not file.filename.endswith(('.txt', '.md', '.pdf')):
                raise HTTPException(status_code=400, detail="支持 .txt、.md 和 .pdf 文件")

            content = await file.read()

            # 处理不同文件类型
            if file.filename.endswith('.pdf'):
                # 这里可以添加PDF解析逻辑
                # 暂时跳过PDF文件
                logger.warning(f"暂不支持PDF文件: {file.filename}")
                continue
            else:
                content = content.decode('utf-8')

            file_contents.append({
                "content": content,
                "filename": file.filename
            })

        if not file_contents:
            raise HTTPException(status_code=400, detail="没有有效的文件内容")

        # 添加到混合索引
        success = search_service.add_documents_from_files(file_contents)

        if success:
            return UploadResponse(
                message=f"成功上传并处理了 {len(file_contents)} 个文件",
                file_count=len(file_contents),
                total_uploaded=len(files)
            )
        else:
            raise HTTPException(status_code=500, detail="文档处理失败")

    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """搜索文档"""
    try:
        results = search_service.search(
            query=request.query,
            limit=request.limit,
            mode=request.mode,
            alpha=request.alpha
        )

        search_results = [
            SearchResult(
                text=result["text"][:300] + "..." if len(result["text"]) > 300 else result["text"],
                score=result["score"],
                source=result["source"],
                mode=result.get("mode", request.mode)
            )
            for result in results
        ]

        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            mode=request.mode,
            alpha=request.alpha if request.mode == "hybrid" else None
        )
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats", response_model=StatsResponse)
async def get_stats():
    """获取系统统计信息"""
    try:
        stats = search_service.get_stats()
        return StatsResponse(**stats)
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy",
        service="中文文档混合检索系统",
        version="2.0.0"
    )

if __name__ == "__main__":
    host = os.getenv("APP_HOST", "0.0.0.0")
    port = int(os.getenv("APP_PORT", "8000"))
    uvicorn.run(app, host=host, port=port)
