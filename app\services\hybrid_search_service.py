import jieba
import uuid
import math
from typing import List, Dict, Any, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
try:
    from qdrant_client import QdrantClient, models
    QDRANT_CLIENT_AVAILABLE = True
except ImportError:
    QDRANT_CLIENT_AVAILABLE = False
    QdrantClient = None
    models = None
from llama_index.core import VectorStoreIndex, StorageContext, Document, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.retrievers.bm25 import BM25Retriever
import logging
import os

logger = logging.getLogger(__name__)

class CollectionInfo:
    """模拟 Qdrant 集合信息对象"""
    def __init__(self, data: dict):
        self.data = data
        self.config = CollectionConfig(data.get('config', {}))

    def get(self, key, default=None):
        """提供字典式访问方法"""
        return self.data.get(key, default)

    def __getitem__(self, key):
        """支持字典式访问"""
        return self.data[key]

    def __contains__(self, key):
        """支持 in 操作符"""
        return key in self.data

class CollectionConfig:
    """模拟 Qdrant 集合配置对象"""
    def __init__(self, config_data: dict):
        self.params = CollectionParams(config_data.get('params', {}))

class CollectionParams:
    """模拟 Qdrant 集合参数对象"""
    def __init__(self, params_data: dict):
        self.sparse_vectors = params_data.get('sparse_vectors', {})
        self.vectors = params_data.get('vectors', {})

class SimpleQdrantClient:
    """简单的Qdrant REST API客户端，兼容LlamaIndex QdrantVectorStore"""

    def __init__(self, url: str):
        self.url = url.rstrip('/')
        import requests
        self.session = requests.Session()

    def get_collections(self):
        """获取所有集合"""
        response = self.session.get(f"{self.url}/collections")
        response.raise_for_status()
        return response.json()

    def collection_exists(self, collection_name: str) -> bool:
        """检查集合是否存在"""
        try:
            response = self.session.get(f"{self.url}/collections/{collection_name}")
            return response.status_code == 200
        except Exception:
            return False

    def create_collection(self, collection_name: str, vectors_config: dict, sparse_vectors_config: dict = None):
        """创建集合"""
        data = {
            "vectors": vectors_config,
        }
        if sparse_vectors_config:
            data["sparse_vectors"] = sparse_vectors_config

        response = self.session.put(f"{self.url}/collections/{collection_name}", json=data)
        response.raise_for_status()
        return response.json()

    def get_collection(self, collection_name: str):
        """获取集合信息"""
        response = self.session.get(f"{self.url}/collections/{collection_name}")
        response.raise_for_status()
        collection_data = response.json()["result"]
        return CollectionInfo(collection_data)

    def _convert_vector_to_dict(self, vector):
        """转换向量对象为字典格式"""
        if hasattr(vector, 'indices') and hasattr(vector, 'values'):
            # 这是一个稀疏向量
            return {
                "indices": vector.indices,
                "values": vector.values
            }
        elif isinstance(vector, dict):
            # 已经是字典格式，递归处理嵌套的向量
            result = {}
            for key, value in vector.items():
                if hasattr(value, 'indices') and hasattr(value, 'values'):
                    result[key] = {
                        "indices": value.indices,
                        "values": value.values
                    }
                else:
                    result[key] = value
            return result
        else:
            # 普通向量（列表）
            return vector

    def upsert(self, collection_name: str, points: list, wait: bool = True):
        """插入或更新点"""
        # 转换 PointStruct 对象为字典格式
        converted_points = []
        for point in points:
            if hasattr(point, 'id') and hasattr(point, 'vector') and hasattr(point, 'payload'):
                # 这是一个 PointStruct 对象
                point_dict = {
                    "id": point.id,
                    "vector": self._convert_vector_to_dict(point.vector),
                    "payload": point.payload or {}
                }
                converted_points.append(point_dict)
            else:
                # 假设已经是字典格式
                converted_points.append(point)

        data = {
            "points": converted_points
        }
        if wait:
            data["wait"] = wait

        response = self.session.put(f"{self.url}/collections/{collection_name}/points", json=data)
        response.raise_for_status()
        return response.json()

    def search(self, collection_name: str, vector: list, limit: int = 10, with_payload: bool = True, with_vectors: bool = False, **kwargs):
        """搜索向量"""
        data = {
            "vector": vector,
            "limit": limit,
            "with_payload": with_payload,
            "with_vectors": with_vectors
        }
        data.update(kwargs)

        response = self.session.post(f"{self.url}/collections/{collection_name}/points/search", json=data)
        response.raise_for_status()
        return response.json()

    def delete_collection(self, collection_name: str):
        """删除集合"""
        response = self.session.delete(f"{self.url}/collections/{collection_name}")
        response.raise_for_status()
        return response.json()

    def upload_points(self, collection_name: str, points: list, parallel: int = 1, max_retries: int = 3, wait: bool = True, batch_size: int = 64, **kwargs):
        """批量上传点（LlamaIndex 兼容方法）"""
        # 忽略额外的参数，只使用我们支持的参数
        return self.upsert(collection_name=collection_name, points=points, wait=wait)

    def search_batch(self, collection_name: str, requests: list, **kwargs):
        """批量搜索（LlamaIndex 兼容方法）"""
        results = []
        for request in requests:
            # 转换搜索请求格式
            # 安全地获取属性，支持对象和字典两种格式
            if hasattr(request, 'vector'):
                vector = self._convert_vector_to_dict(request.vector)
            elif isinstance(request, dict) and 'vector' in request:
                vector = request['vector']
            else:
                vector = None

            if hasattr(request, 'limit'):
                limit = request.limit
            elif isinstance(request, dict) and 'limit' in request:
                limit = request['limit']
            else:
                limit = 10

            if hasattr(request, 'with_payload'):
                with_payload = request.with_payload
            elif isinstance(request, dict) and 'with_payload' in request:
                with_payload = request['with_payload']
            else:
                with_payload = True

            if hasattr(request, 'with_vectors'):
                with_vectors = request.with_vectors
            elif isinstance(request, dict) and 'with_vectors' in request:
                with_vectors = request['with_vectors']
            else:
                with_vectors = False

            search_params = {
                "vector": vector,
                "limit": limit,
                "with_payload": with_payload,
                "with_vectors": with_vectors
            }

            # 添加其他参数
            if hasattr(request, 'filter'):
                search_params['filter'] = request.filter
            elif isinstance(request, dict) and 'filter' in request:
                search_params['filter'] = request['filter']

            result = self.search(collection_name=collection_name, **search_params)
            results.append(result)

        return results

class HybridSearchService:
    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        self.qdrant_url = qdrant_url
        self.client = None
        self.collection_name = "hybsearchdoc"
        self.tfidf = None
        self.embed_model = None
        self.vector_store = None
        self.index = None
        self.bm25_retriever = None
        self.setup_models()
        self.setup_qdrant_client()
        self.setup_vector_store()

    def setup_qdrant_client(self):
        """设置Qdrant客户端，带重试机制"""
        import time
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 先用requests测试连接
                import requests
                test_response = requests.get(f"{self.qdrant_url}/collections", timeout=5)
                if test_response.status_code != 200:
                    raise Exception(f"HTTP test failed: {test_response.status_code}")

                # 直接使用增强的SimpleQdrantClient（已兼容LlamaIndex）
                try:
                    self.client = SimpleQdrantClient(self.qdrant_url)
                    # 测试连接
                    self.client.get_collections()
                    logger.info(f"成功使用增强的REST API客户端连接到Qdrant: {self.qdrant_url}")
                    break
                except Exception as e1:
                    logger.warning(f"增强REST API连接失败: {e1}")

                    # 如果增强客户端失败，尝试官方客户端作为备用
                    if QDRANT_CLIENT_AVAILABLE and QdrantClient:
                        try:
                            # 尝试使用URL方式连接
                            self.client = QdrantClient(url=self.qdrant_url, prefer_grpc=False)
                            # 测试连接
                            self.client.get_collections()
                            logger.info(f"成功使用官方qdrant-client连接到Qdrant: {self.qdrant_url}")
                            break
                        except Exception as e2:
                            logger.warning(f"官方qdrant-client连接失败: {e2}")
                            raise e1  # 抛出增强客户端的错误
                    else:
                        raise e1

            except Exception as e:
                logger.warning(f"连接Qdrant失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间：2, 4, 6秒
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error("无法连接到Qdrant，将使用离线模式")
                    self.client = None

    def setup_models(self):
        """设置嵌入模型和TF-IDF"""
        # 设置 OpenAI 嵌入模型
        from llama_index.embeddings.openai import OpenAIEmbedding

        # 从环境变量获取 API 密钥，如果没有则使用默认值
        api_key = os.getenv("OPENAI_API_KEY", "sk-zbruJKss5bHXcDraW0mcHI5xcYhUJ4fi80kxV16IWly1F3Nn")
        api_base = os.getenv("OPENAI_API_BASE", "https://api.openai-proxy.org/v1")

        self.embed_model = OpenAIEmbedding(
            model="text-embedding-3-small",
            api_key=api_key,
            api_base=api_base
        )

        Settings.embed_model = self.embed_model
        logger.info(f"使用 OpenAI text-embedding-3-small 模型，API Base: {api_base}")

        # 初始化TF-IDF向量化器
        self.tfidf = TfidfVectorizer(
            tokenizer=self.jieba_tokenizer,
            analyzer="word",
            token_pattern=None,
            lowercase=False
        )

    def jieba_tokenizer(self, text: str) -> List[str]:
        """jieba分词器"""
        return [tok.strip() for tok in jieba.lcut(text) if tok.strip()]

    def _csr_to_indices_values(self, mat_row) -> Tuple[List[int], List[float]]:
        """将CSR矩阵行转换为indices和values"""
        coo = mat_row.tocoo()
        return coo.col.tolist(), coo.data.tolist()

    def sparse_doc_vectors(self, texts: List[str]) -> Tuple[List[List[int]], List[List[float]]]:
        """生成文档的稀疏向量"""
        if self.tfidf is None:
            return [], []

        mat = self.tfidf.transform(texts)
        indices_list, values_list = [], []

        for i in range(mat.shape[0]):
            indices, values = self._csr_to_indices_values(mat.getrow(i))
            indices_list.append(indices)
            values_list.append(values)

        return indices_list, values_list

    def sparse_query_vectors(self, texts: List[str]) -> Tuple[List[List[int]], List[List[float]]]:
        """生成查询的稀疏向量"""
        return self.sparse_doc_vectors(texts)

    def setup_vector_store(self):
        """设置向量存储"""
        if not self.client:
            logger.warning("Qdrant客户端未连接，跳过向量存储设置")
            return

        # 确保集合存在并配置正确
        try:
            # 检查集合是否存在
            collection_exists = self.client.collection_exists(self.collection_name)

            if collection_exists:
                # 删除现有集合以确保配置正确
                logger.info(f"删除现有集合 {self.collection_name} 以重新配置")
                self.client.delete_collection(self.collection_name)

            # 创建支持混合检索的集合，使用LlamaIndex标准命名
            if isinstance(self.client, SimpleQdrantClient):
                # 使用REST API创建集合
                vectors_config = {
                    "text-dense": {
                        "size": 1536,  # OpenAI text-embedding-3-small 的维度
                        "distance": "Cosine"
                    }
                }
                sparse_vectors_config = {
                    "text-sparse": {
                        "index": {"on_disk": False}
                    }
                }
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=vectors_config,
                    sparse_vectors_config=sparse_vectors_config
                )
                logger.info(f"使用REST API创建混合搜索集合: {self.collection_name}")
            else:
                # 使用qdrant-client创建集合
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config={
                        "text-dense": models.VectorParams(
                            size=1536,  # OpenAI text-embedding-3-small 的维度
                            distance=models.Distance.COSINE
                        )
                    },
                    sparse_vectors_config={
                        "text-sparse": models.SparseVectorParams(
                            index=models.SparseIndexParams(on_disk=False)
                        )
                    }
                )
                logger.info(f"使用qdrant-client创建混合搜索集合: {self.collection_name}")
        except Exception as e:
            logger.error(f"设置集合失败: {e}")
            return

        # 创建向量存储
        try:
            self.vector_store = QdrantVectorStore(
                collection_name=self.collection_name,
                client=self.client,
                enable_hybrid=True,
                sparse_doc_fn=self.sparse_doc_vectors,
                sparse_query_fn=self.sparse_query_vectors
            )
            logger.info("向量存储设置成功")
        except Exception as e:
            logger.error(f"向量存储设置失败: {e}")
            self.vector_store = None

    def add_documents_from_files(self, file_contents: List[Dict[str, str]]) -> bool:
        """从文件内容添加文档"""
        try:
            # 创建文档对象
            documents = []
            all_texts = []

            for file_data in file_contents:
                content = file_data["content"]
                doc = Document(
                    text=content,
                    metadata={"filename": file_data["filename"]},
                    doc_id=str(uuid.uuid4())
                )
                documents.append(doc)
                all_texts.append(content)

            # 使用文档分割器
            splitter = SentenceSplitter(
                chunk_size=512,
                chunk_overlap=50
            )

            # 获取分割后的节点
            nodes = splitter.get_nodes_from_documents(documents)
            node_texts = [node.text for node in nodes]

            # 训练TF-IDF模型
            if node_texts:
                self.tfidf.fit(node_texts)
                vocab_size = len(self.tfidf.vocabulary_)
                logger.info(f"TF-IDF词汇表大小: {vocab_size}")

            # 创建BM25检索器（总是可用）
            self.bm25_retriever = BM25Retriever.from_defaults(
                nodes=nodes,
                similarity_top_k=10
            )

            # 如果有向量存储，创建向量索引
            if self.vector_store:
                try:
                    # 创建存储上下文
                    storage_context = StorageContext.from_defaults(
                        vector_store=self.vector_store
                    )

                    # 创建索引（自动处理密向量嵌入和稀疏向量）
                    self.index = VectorStoreIndex.from_documents(
                        documents,
                        storage_context=storage_context,
                        transformations=[splitter]
                    )
                    logger.info("向量索引创建成功")
                except Exception as e:
                    import traceback
                    logger.error(f"向量索引创建失败: {e}")
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
                    self.index = None
            else:
                logger.warning("向量存储不可用，仅使用BM25检索")
                self.index = None

            logger.info(f"成功处理 {len(documents)} 个文档，生成 {len(nodes)} 个节点")
            return True

        except Exception as e:
            logger.error(f"添加文档时出错: {e}")
            return False

    def search(self, query: str, limit: int = 5, mode: str = "hybrid", alpha: float = 0.5) -> List[Dict[str, Any]]:
        """搜索文档

        Args:
            query: 查询文本
            limit: 返回结果数量
            mode: 搜索模式 ("hybrid", "sparse", "dense", "bm25")
            alpha: 混合搜索中稀疏向量的权重 (0.0-1.0)
        """
        try:
            # 如果没有向量索引，强制使用BM25
            if not self.index and mode != "bm25":
                logger.warning(f"向量索引不可用，将 {mode} 模式切换为 BM25")
                mode = "bm25"

            # 如果没有任何检索器可用，返回空结果
            if mode == "bm25" and not self.bm25_retriever:
                logger.warning("BM25检索器不可用")
                return []

            if mode == "bm25" and self.bm25_retriever:
                # 纯BM25搜索（备用方案）
                query_tokens = " ".join(jieba.cut(query))
                nodes = self.bm25_retriever.retrieve(query_tokens)

                results = []
                for node in nodes[:limit]:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "bm25"
                    })
                return results

            elif mode == "hybrid":
                # 混合搜索（稀疏向量 + 密向量）
                if not self.index:
                    logger.warning("向量索引不可用，无法进行混合搜索")
                    return []

                retriever = self.index.as_retriever(
                    similarity_top_k=limit,
                    sparse_top_k=limit * 2,  # 稀疏向量多取一些候选
                    vector_store_query_mode="hybrid"
                )

                nodes = retriever.retrieve(query)

                results = []
                for node in nodes[:limit]:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "hybrid"
                    })
                return results

            elif mode == "sparse":
                # 纯稀疏向量搜索
                if not self.index:
                    logger.warning("向量索引不可用，无法进行稀疏向量搜索")
                    return []

                retriever = self.index.as_retriever(
                    similarity_top_k=0,  # 不使用密向量
                    sparse_top_k=limit,
                    vector_store_query_mode="hybrid"
                )

                nodes = retriever.retrieve(query)

                results = []
                for node in nodes:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "sparse"
                    })
                return results

            else:  # mode == "dense"
                # 纯密向量搜索
                if not self.index:
                    logger.warning("向量索引不可用，无法进行密向量搜索")
                    return []

                retriever = self.index.as_retriever(
                    similarity_top_k=limit,
                    vector_store_query_mode="default"
                )

                nodes = retriever.retrieve(query)

                results = []
                for node in nodes:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "dense"
                    })
                return results

        except Exception as e:
            logger.error(f"搜索时出错: {e}")
            return []

    def load_documents_from_directory(self, directory_path: str = "data/documents") -> bool:
        """从指定目录加载所有文档"""
        try:
            if not os.path.exists(directory_path):
                logger.warning(f"文档目录不存在: {directory_path}")
                return False

            file_contents = []
            supported_extensions = ('.txt', '.md')

            for filename in os.listdir(directory_path):
                if filename.endswith(supported_extensions):
                    file_path = os.path.join(directory_path, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            file_contents.append({
                                "content": content,
                                "filename": filename
                            })
                            logger.info(f"加载文档: {filename}")
                    except Exception as e:
                        logger.error(f"读取文件 {filename} 时出错: {e}")
                        continue

            if file_contents:
                success = self.add_documents_from_files(file_contents)
                if success:
                    logger.info(f"成功从目录 {directory_path} 加载了 {len(file_contents)} 个文档")
                return success
            else:
                logger.warning(f"目录 {directory_path} 中没有找到支持的文档文件")
                return False

        except Exception as e:
            logger.error(f"从目录加载文档时出错: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            vocab_size = len(self.tfidf.vocabulary_) if self.tfidf and hasattr(self.tfidf, 'vocabulary_') else 0

            if not self.client:
                return {
                    "collection_name": self.collection_name,
                    "points_count": 0,
                    "vectors_count": 0,
                    "vocab_size": vocab_size,
                    "status": "offline_mode",
                    "bm25_available": self.bm25_retriever is not None
                }

            if isinstance(self.client, SimpleQdrantClient):
                collection_info = self.client.get_collection(self.collection_name)
                points_count = collection_info.get("points_count", 0)
                vectors_count = collection_info.get("vectors_count", 0)
            else:
                collection_info = self.client.get_collection(self.collection_name)
                points_count = collection_info.points_count
                vectors_count = collection_info.vectors_count

            return {
                "collection_name": self.collection_name,
                "points_count": points_count,
                "vectors_count": vectors_count,
                "vocab_size": vocab_size,
                "status": "ready" if self.index else "bm25_only",
                "bm25_available": self.bm25_retriever is not None
            }
        except Exception as e:
            logger.error(f"获取统计信息时出错: {e}")
            vocab_size = len(self.tfidf.vocabulary_) if self.tfidf and hasattr(self.tfidf, 'vocabulary_') else 0
            return {
                "collection_name": self.collection_name,
                "points_count": 0,
                "vectors_count": 0,
                "vocab_size": vocab_size,
                "status": "error",
                "error": str(e),
                "bm25_available": self.bm25_retriever is not None
            }
